package top.continew.admin.biz.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.admin.biz.mapper.CustomerExtraMapper;
import top.continew.admin.biz.model.entity.CustomerExtraDO;
import top.continew.admin.biz.model.req.CustomerExtraReq;
import top.continew.admin.biz.service.CustomerExtraService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 客户拓展表业务实现
 *
 * <AUTHOR>
 * @since 2025/09/09 09:52
 */
@Service
@RequiredArgsConstructor
public class CustomerExtraServiceImpl extends ServiceImpl<CustomerExtraMapper, CustomerExtraDO>
        implements CustomerExtraService {

    private final CustomerService customerService;


    @Override
    public void updateBalanceReminderAmountThresholdById(CustomerExtraReq req) {
        List<CustomerExtraDO> list = new ArrayList<CustomerExtraDO>();
        req.getIds().forEach(item -> {
            CustomerDO order = customerService.getById(item);
            CheckUtils.throwIfNull(order, "客户不存在");
            //获取当前的客户是否配置过
            CustomerExtraDO customerExtraDO = this.getOne(Wrappers.<CustomerExtraDO>lambdaQuery().eq(CustomerExtraDO::getCustomerId, item));
            if (customerExtraDO == null) {
                customerExtraDO = new CustomerExtraDO();
                customerExtraDO.setCustomerId(item);
            }
            if (req.getIsSetThreshold() == 1) {
                customerExtraDO.setBalanceAlertThreshold(req.getBalanceAlertThreshold());
            } else if (req.getIsSetThreshold() == 0) {
               customerExtraDO.setBalanceAlertThreshold(new BigDecimal(-1));
            }
            list.add(customerExtraDO);
        });
        this.baseMapper.insertOrUpdate(list);
    }


}