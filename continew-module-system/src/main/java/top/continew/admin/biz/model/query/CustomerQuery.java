/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.model.query;

import java.io.Serial;
import java.io.Serializable;
import java.time.*;
import java.util.List;

import cn.hutool.core.date.DatePattern;
import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import top.continew.admin.biz.enums.CustomerTypeEnum;
import top.continew.starter.data.core.annotation.Query;
import top.continew.starter.data.core.enums.QueryType;

/**
 * 客户查询条件
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
@Data
@Schema(description = "客户查询条件")
public class CustomerQuery implements Serializable  {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @Schema(description = "名称")
    @Query(type = QueryType.LIKE)
    private String name;

    /**
     * 名称
     */
    @Schema(description = "关联中介")
    @Query(type = QueryType.EQ)
    private Long agentId;

    /**
     * 关联商务
     */
    @Schema(description = "关联商务")
    @Query(type = QueryType.EQ)
    private Long businessUserId;

    /**
     * 客户状态
     */
    @Query(type = QueryType.EQ)
    @Schema(description = "客户状态")
    private Integer status;

    /**
     * 标签ID
     */
    @Schema(description = "标签ID")
    private Long tagId;

    /**
     * 客户类型：1是正式客户，2是潜在客户
     */
    private CustomerTypeEnum type;

    /**
     * 客户来源ID
     */
    private Long sourceId;

    /**
     * 客户行业
     */
    private Integer industry;

    /**
     * 客户城市
     */
    private String city;


    @Query(type = QueryType.EQ)
    private Boolean isSelfAccount;

    private List<Long> customerIds;
    
    /**
     * 客户类别（new:新客户, old:老客户）
     */
    @Schema(description = "客户类别")
    private String customerCategory;

    @Query(type = QueryType.EQ)
    private Integer settleType;

    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Query(type = QueryType.EQ)
    private LocalDateTime[] cooperateTime;

    /**
     * 最近合作时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Query(type = QueryType.EQ)
    private LocalDateTime[] lastCooperateTime;

    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @Query(type = QueryType.EQ)
    private LocalDateTime[] createTime;

    /**
     * 终止时间
     */
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private LocalDateTime[] terminateTime;
    @Query(type = QueryType.EQ)
    private Integer businessType;

    private Integer balanceAlertThreshold;

}