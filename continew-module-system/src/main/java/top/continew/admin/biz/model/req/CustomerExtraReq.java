package top.continew.admin.biz.model.req;

import java.io.Serial;
import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.*;

import lombok.Data;

import io.swagger.v3.oas.annotations.media.Schema;


import top.continew.starter.extension.crud.model.req.BaseReq;

/**
 * 创建或修改客户拓展表参数
 *
 * <AUTHOR>
 * @since 2025/09/09 09:52
 */
@Data
@Schema(description = "创建或修改客户拓展表参数")
public class CustomerExtraReq extends BaseReq {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    @NotEmpty(message = "ID列表不能为空")
    @Schema(description = "ID列表")
    private List<Long> ids;

    /**
     * 余额提醒金额阈值
     */
    @Schema(description = "余额提醒金额阈值")
    @Min(value = 0, message = "阈值金额不能小于0")
    private BigDecimal balanceAlertThreshold;

    @NotNull(message = "必须选择是否设置阈值 1设置  0 不设置")
    @Schema(description = "是否设置阈值")
    private Integer isSetThreshold;


}