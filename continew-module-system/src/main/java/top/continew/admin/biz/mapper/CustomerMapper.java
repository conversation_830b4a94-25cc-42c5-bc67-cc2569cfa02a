/*
 * Copyright (c) 2022-present Charles7c Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.CustomerOrderStatisticsQuery;
import top.continew.admin.biz.model.query.CustomerQuery;
import top.continew.admin.biz.model.query.CustomerStatReportQuery;
import top.continew.admin.biz.model.resp.*;
import top.continew.starter.data.mp.base.BaseMapper;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户 Mapper
 *
 * <AUTHOR>
 * @since 2024/12/30 17:56
 */
public interface CustomerMapper extends BaseMapper<CustomerDO> {
    /**
     * 分页查询客户列表
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<CustomerResp> pageCustomers(@Param("page") IPage<CustomerDO> page, @Param("query") CustomerQuery query);

    IPage<ToufangCustomerResp> selectToufangCustomerPage(@Param("page") IPage<CustomerDO> page,
                                                         @Param(Constants.WRAPPER) QueryWrapper<CustomerDO> queryWrapper, @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    List<ToufangCustomerResp> selectToufangCustomerList(@Param(Constants.WRAPPER) QueryWrapper<CustomerDO> queryWrapper, @Param("start") LocalDateTime start, @Param("end") LocalDateTime end);

    /**
     * 查询报告分页列表
     *
     * @return
     */
    IPage<CustomerStatReportResp> selectCustomerStatReport(@Param("page") IPage<CustomerDO> page,
                                                           @Param("customerId") Long customerId,
                                                           @Param("start") LocalDateTime start,
                                                           @Param("end") LocalDateTime end,
                                                           @Param("isSelf") Boolean isSelf,
                                                           @Param("settleType") Integer settleType,
                                                           @Param("businessType") Integer businessType,
                                                           @Param("customerIndustry") Integer customerIndustry,
                                                           @Param("sortField") String sortField,
                                                           @Param("ascSortFlag") Boolean ascSortFlag);

    /**
     * 查询报告列表
     *
     * @return
     */
    List<CustomerStatReportResp> listCustomerStatReport(@Param("customerId") Long customerId,
                                                        @Param("start") LocalDateTime start,
                                                        @Param("end") LocalDateTime end,
                                                        @Param("isSelf") Boolean isSelf,
                                                        @Param("settleType") Integer settleType,
                                                        @Param("businessType") Integer businessType,
                                                        @Param("customerIndustry") Integer customerIndustry);

    /**
     * 客户报告汇总
     *
     * @param query
     * @return
     */
    CustomerStatSummaryResp getCustomerStatReportSummary(@Param("query") CustomerStatReportQuery query);

    /**
     * 查询每日报告分页列表
     *
     * @return
     */
    IPage<CustomerDailyStatReportResp> selectCustomerDailyStatReport(@Param("page") IPage<CustomerDO> page,
                                                                     @Param("customerId") Long customerId,
                                                                     @Param("start") LocalDateTime start,
                                                                     @Param("end") LocalDateTime end);

    /**
     * 查询每日报告列表
     *
     * @return
     */
    List<CustomerDailyStatReportResp> listCustomerDailyStatReport(@Param("customerId") Long customerId,
                                                                  @Param("start") LocalDateTime start,
                                                                  @Param("end") LocalDateTime end);

    @Select("SELECT IFNULL(sum(balance), 0) FROM biz_customer where is_self_account = false")
    BigDecimal getTotalBalance();

    IPage<CustomerOrderStatisticsResp> selectCustomerOrderStatisticsPage(@Param("page") IPage<CustomerOrderStatisticsQuery> page,
                                                                         CustomerOrderStatisticsQuery query);

    @Select("SELECT IFNULL(sum(balance), 0) FROM biz_customer where id in (select o.customer_id from biz_customer_withdraw_order o where o.status = 1)")
    BigDecimal getRefundingCustomerBalance();

    IPage<CustomerProfitStatResp> selectCustomerProfitStatPage(@Param("page") IPage<CustomerProfitStatResp> page,
                                                               @Param(Constants.WRAPPER) QueryWrapper<CustomerProfitStatResp> queryWrapper);

    List<CustomerProfitStatResp> selectCustomerProfitStatList(@Param(Constants.WRAPPER) QueryWrapper<CustomerProfitStatResp> queryWrapper);

    String selectCustomerNameByAdAccountId(@Param("adAccountId") String adAccountId);
}