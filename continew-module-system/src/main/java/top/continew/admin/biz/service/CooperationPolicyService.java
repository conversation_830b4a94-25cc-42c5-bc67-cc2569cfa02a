package top.continew.admin.biz.service;

import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.CooperationPolicyQuery;
import top.continew.admin.biz.model.req.CooperationPolicyReq;
import top.continew.admin.biz.model.resp.CooperationPolicyDetailResp;
import top.continew.admin.biz.model.resp.CooperationPolicyResp;

/**customer
 * 渠道合作政策业务接口
 *
 * <AUTHOR>
 * @since 2025/09/08 15:54
 */
public interface CooperationPolicyService extends BaseService<CooperationPolicyResp, CooperationPolicyDetailResp, CooperationPolicyQuery, CooperationPolicyReq> {}