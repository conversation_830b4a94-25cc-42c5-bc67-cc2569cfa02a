package top.continew.admin.biz.model.entity;

import java.io.Serial;
import java.math.BigDecimal;

import lombok.Data;

import com.baomidou.mybatisplus.annotation.TableName;

import top.continew.starter.extension.crud.model.entity.BaseDO;

/**
 * 客户拓展表实体
 *
 * <AUTHOR>
 * @since 2025/09/09 09:52
 */
@Data
@TableName("biz_customer_extra")
public class CustomerExtraDO extends BaseDO {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 关联客户
     */
    private Long customerId;

    /**
     * 余额提醒金额阈值
     */
    private BigDecimal balanceAlertThreshold;
}